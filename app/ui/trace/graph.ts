import { DataType, type Table } from "apache-arrow";
import { TransactionIdField } from "#/utils/duckdb";
import { z } from "zod";
import { DiffLeftField, DiffRightField } from "#/utils/diffs/diff-objects";
import { allKeys } from "#/utils/all-keys";
import {
  type Span,
  type SpanData,
  type SpanScore,
  spanAttributesSchema,
  objectReferenceSchema,
} from "@braintrust/local";
import { BN } from "apache-arrow/util/bn";

export { spanMetricNames, systemMetricNames } from "@braintrust/local";
export type {
  Span,
  SpanData,
  SpanScore,
  SpanMetrics,
  SystemMetrics,
  ConfiguredScore,
} from "@braintrust/local";

type BaseTrace<Preview extends boolean = true> = {
  root: Preview extends true ? PreviewSpan : Span;
  spans: Record<string, Preview extends true ? PreviewSpan : Span>;
  isPlaceholder?: boolean;
};

export type PreviewTrace = BaseTrace;
export type LoadedTrace = BaseTrace<false>;

export type Trace = PreviewTrace | LoadedTrace;

export function sortSpanChildren<T extends Span | PreviewSpan>(span: T) {
  span.children.sort((a, b) => {
    const startDiff =
      (a.data.metrics?.start ?? 0) - (b.data.metrics?.start ?? 0);
    if (startDiff !== 0) {
      return startDiff;
    }

    const counterDiff =
      (a.data.span_attributes.exec_counter ?? 0) -
      (b.data.span_attributes.exec_counter ?? 0);
    return counterDiff;
  });
}

export function parseScoreData(
  spanId: string,
  scores: Record<string, number | null>,
): Record<string, SpanScore> {
  return Object.fromEntries(
    Object.entries(scores)
      .filter(([_key, value]) => value !== null)
      .map(([key, value]) => [
        key,
        {
          left: [],
          right: [value!],
          isDiff: false,
          spanId,
        },
      ]),
  );
}

export function diffScores(
  left: Record<string, SpanScore>,
  right: Record<string, SpanScore>,
): Record<string, SpanScore> {
  return Object.fromEntries(
    allKeys(right, left).map((key) => {
      // diffScores should only be called on non-diff SpanScores, so both objects should
      // have an empty left array.
      console.assert(
        !left[key]?.left || left[key].left.length === 0,
        "left's left array not empty",
      );
      console.assert(
        !right[key]?.left || right[key].left.length === 0,
        "right's left array not empty",
      );
      return [
        key,
        {
          left: left[key]?.right ?? [],
          right: right?.[key]?.right ?? [],
          isDiff: true,
          spanId: right?.[key]?.spanId,
        },
      ];
    }),
  );
}

function mergeScores(
  s1: Record<string, SpanScore>,
  s2: Record<string, SpanScore>,
): Record<string, SpanScore> {
  return Object.fromEntries(
    allKeys(s1, s2).map((key) => [
      key,
      {
        left: (s1[key]?.left ?? []).concat(s2[key]?.left ?? []),
        right: (s1[key]?.right ?? []).concat(s2[key]?.right ?? []),
        // These two should always be the same.
        isDiff: s1[key]?.isDiff || s2[key]?.isDiff,
        spanId:
          s1[key]?.spanId && s2[key]?.spanId
            ? undefined
            : s1[key]?.spanId || s2[key]?.spanId,
      },
    ]),
  );
}

const safeParseString = (val: unknown) => {
  const parsed = z.string().safeParse(val);
  return parsed.success ? parsed.data : "";
};

export function buildTrace(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  rows: Table<any>,
  customColumns?: Record<string, Record<string, unknown>> | null,
): LoadedTrace | null {
  if (rows.numRows === 0) {
    return null;
  }

  const fieldTypes = new Map<string, DataType>(
    rows.schema.fields.map((field) => [field.name, field.type]),
  );
  const spans: Record<string, Span> = {};
  let root: Span | null = null;

  const rowArray = rows.toArray();

  for (const row of rowArray) {
    const {
      metrics,
      scores,
      span_attributes,
      tags,
      prompt_cost,
      completion_cost,
      model: _ignore_model,
      model_1: _ignore_model_1,
      input_cost_per_mil_tokens: _ignore_input_cost,
      output_cost_per_mil_tokens: _ignore_output_cost,
      origin,
      ...data
    } = row.toJSON();
    const parsedScores = scores
      ? z.record(z.number().nullable()).parse(JSON.parse(scores))
      : {};
    console.assert(row.span_id, "span_id not set");
    console.assert(row.root_span_id, "root_span_id not set");
    const tagsParsed = z
      .array(z.string())
      .safeParse(tags ? JSON.parse(tags) : []);
    const parsedMetrics = metrics ? JSON.parse(metrics) : undefined;
    const estimated_cost =
      prompt_cost || completion_cost
        ? (prompt_cost || 0) + (completion_cost || 0)
        : undefined;

    const parsedAttributes = span_attributes
      ? spanAttributesSchema.safeParse(JSON.parse(span_attributes))
      : undefined;

    const parsedOrigin = origin
      ? objectReferenceSchema.safeParse(JSON.parse(origin))
      : undefined;

    const parsedData = Object.fromEntries(
      Object.entries({
        ...data,
        ...(row.is_root && customColumns ? customColumns[row.span_id] : {}),
      }).map(([key, val]) => {
        const type = fieldTypes.get(key);
        try {
          if (
            DataType.isDecimal(type) &&
            val instanceof Uint32Array &&
            type.scale
          ) {
            const bnValue = new BN(val, true);
            return [key, bnValue.valueOf(type.scale)];
          }
        } catch {}
        return [key, val];
      }),
    );

    const baseSpan: Span = {
      id: row.id,
      // This way, the span_id and root_span_id are always set
      span_id: row.span_id,
      root_span_id: row.root_span_id,
      data: {
        span_id: safeParseString(parsedData.span_id),
        id: safeParseString(parsedData.id),
        _xact_id: safeParseString(parsedData._xact_id),
        metrics: {
          ...parsedMetrics,
          ...(estimated_cost ? { estimated_cost } : {}),
        },
        scores: parsedScores,
        span_attributes: !parsedAttributes
          ? { name: "<unspecified>" }
          : !parsedAttributes.success
            ? { name: "<invalid span_attributes>" }
            : parsedAttributes.data,
        // Somewhat subtley, undefined means that the backend does not support tags, whereas
        // null means that the span has no tags.
        tags: tagsParsed.success ? tagsParsed.data : undefined,
        ...(parsedOrigin?.success ? { origin: parsedOrigin.data } : {}),
        ...parsedData,
      },
      scores: parseScoreData(row.span_id, parsedScores),
      parent_span_id: null,
      span_parents: row.span_parents ? JSON.parse(row.span_parents) : [],
      children: [],
    };
    const span = baseSpan;
    spans[row.span_id] = span;

    if (span.span_parents?.length === 0) {
      console.assert(root === null, "root already set");
      root = span;
    }
  }

  if (!root) {
    throw new Error("root span not found");
  }

  for (const row of rowArray) {
    if (root.root_span_id !== row.root_span_id) {
      // This should not be possible because the caller of buildTrace should be
      // filtering for rows that match one of the root span rows, so the root
      // span row itself must exist.
      throw new Error(
        `root span id ${row.root_span_id} for span ${row.span_id} does not match root span ${root.root_span_id}. All span ids: ${JSON.stringify(Object.keys(spans), null, 2)}`,
      );
    }
  }

  const sortedSpans = topSortSpans(spans);
  const topSortIndex: Map<string, number> = new Map();
  for (const [idx, span] of sortedSpans.entries()) {
    topSortIndex.set(span.span_id, idx);
  }

  for (const row of rowArray) {
    const span = spans[row.span_id];
    const span_parents = span.span_parents ?? [];
    if (span_parents.length > 0) {
      // Even though a span can technically have multiple parents, this
      // functionality is not used anywhere so we only handle the case of a
      // single parent.
      if (span_parents.length > 1) {
        console.warn("Only using first parent of span", row.span_id);
      }

      if (
        topSortIndex.get(span_parents[0])! > topSortIndex.get(span.span_id)!
      ) {
        console.warn(
          `Cycle detected in span graph at span ${span.span_id}. Skipping its parent.`,
        );
        continue;
      }

      // If we find the actual parent span, use it and set `span.parent_span_id`
      // to point to it. Otherwise, we have an orphan span which we put
      // underneath the root_span for display purposes, but we don't set
      // `span.parent_span_id` to indicate that it's an orphan.
      const parentSpan = (() => {
        const parent_span_id = span_parents[0];
        if (parent_span_id in spans) {
          const parentSpan = spans[parent_span_id];
          span.parent_span_id = parentSpan.span_id;
          return parentSpan;
        } else {
          console.warn(
            `parent span ${parent_span_id} not found for span ${span.span_id}. Using root span ${root.span_id} instead`,
          );
          return root;
        }
      })();
      parentSpan.children.push(span);
    }
  }

  const colored: Record<string, boolean> = {};
  for (const span of Object.values(spans)) {
    fillSpanScores(span, colored);
  }

  // For each span, sort its children by start time
  for (const span of Object.values(spans)) {
    sortSpanChildren(span);
  }

  if (!root) {
    throw new Error("root span not found");
  }

  return { root: root!, spans };
}

const MAX_DFS_DEPTH = 3500;
// We need to DFS the scores to properly ensure that we compute (and propagate)
// child scores before parent scores.
export function fillSpanScores<T extends Span | PreviewSpan>(
  span: T,
  colored: Record<string, boolean>,
  depth: number = 0,
) {
  if (depth > MAX_DFS_DEPTH) {
    console.warn("merging scores max depth reached");
    return;
  }
  if (colored[span.span_id]) {
    return;
  }

  for (const child of span.children) {
    fillSpanScores(child, colored, depth + 1);
    span.scores = mergeScores(span.scores, child.scores);
  }

  colored[span.span_id] = true;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function construct_diff(left: any, right: any) {
  return {
    [DiffLeftField]: left,
    [DiffRightField]: right,
  };
}

export function diffData(
  left: PreviewSpanData & Record<string, unknown>,
  right: PreviewSpanData & Record<string, unknown>,
): SpanData {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const ret: { [key: string]: any } = {};
  for (const key of Object.keys(right)) {
    ret[key] = construct_diff(left[key], right[key]);
  }

  for (const key of Object.keys(left)) {
    if (!ret[key]) {
      ret[key] = construct_diff(left[key], right[key]);
    }
  }

  return {
    ...ret,
    scores: construct_diff(left.scores, right.scores),
    error: right.error,
    created: right.created,
    tags: right.tags,
    id: right.id,
    [TransactionIdField]: right[TransactionIdField],
    span_id: right.span_id,
    span_attributes: right.span_attributes,
  };
}

export function initializeEmptySpan(span: PreviewSpan | Span): Span {
  return {
    id: span.id,
    span_id: span.span_id,
    root_span_id: span.root_span_id,
    data: {
      id: span.data.id,
      [TransactionIdField]: span.data[TransactionIdField],
      span_id: span.data.span_id,
      span_attributes: span.data.span_attributes,
      scores: {},
    },
    scores: {},
    parent_span_id: span.parent_span_id,
    children: [],
  };
}

function constructSpanChildrenMap(
  children: PreviewSpan[],
): Record<string, PreviewSpan> {
  const ret: Record<string, PreviewSpan> = {};
  const nameMap: Record<string, number> = {};

  for (const child of children) {
    const name = child.data.span_attributes.name ?? "";
    if (nameMap[name]) {
      nameMap[name]++;
    } else {
      nameMap[name] = 1;
    }

    ret[JSON.stringify({ name, idx: nameMap[name] })] = child;
  }

  return ret;
}

/** mapping for span ids between base and comparison spans */
export type SpanIdsMap = Record<string, { id: string; spanId: string }>;

export function diffSpans(
  spans: Record<string, PreviewSpan>,
  spanIdsMap: SpanIdsMap,
  left?: PreviewSpan,
  right?: PreviewSpan,
): PreviewSpan {
  if (right && spans[right.span_id]) {
    return spans[right.span_id];
  } else if (left && spans[left.span_id]) {
    return spans[left.span_id];
  }

  if (!left && !right) {
    throw new Error("no spans");
  }

  if (!left) {
    left = makePreviewSpan(initializeEmptySpan(right!));
  }
  if (!right) {
    right = makePreviewSpan(initializeEmptySpan(left!));
  }

  const ret: PreviewSpan = {
    ...right,
    data: {
      ...diffData(left.data, right.data),
      // make the types work
      error: right.data.error,
      created: right.data.created,
      model: right.data.model,
    },
    scores: diffScores(left.scores, right.scores),
    children: [],
  };
  spanIdsMap[right.span_id] = { id: left.id, spanId: left.span_id };
  spanIdsMap[left.span_id] = { id: right.id, spanId: right.span_id };

  const leftNames = constructSpanChildrenMap(left.children);
  const rightNames = constructSpanChildrenMap(right.children);

  const allNames = new Set([
    ...Object.keys(leftNames),
    ...Object.keys(rightNames),
  ]);

  for (const name of allNames) {
    ret.children.push(
      diffSpans(spans, spanIdsMap, leftNames[name], rightNames[name]),
    );
  }
  sortSpanChildren(ret);
  spans[ret.span_id] = ret;

  return ret;
}

export function makeDiffSpan(_right?: Span, _left?: Span): Span {
  const left = _left ?? initializeEmptySpan(_right!);
  const right = _right ?? initializeEmptySpan(_left!);

  return {
    ...right,
    data: {
      ...diffData(
        {
          ...left.data,
          // make the types work
          error: left.data.error,
          created: left.data.created,
          model: left.data.model,
        },
        {
          ...right.data,
          // make the types work
          error: right.data.error,
          created: right.data.created,
          model: right.data.model,
        },
      ),
    },
    scores: diffScores(left.scores, right.scores),
    children: [],
  };
}

export function diffTraces(
  left: PreviewTrace | null,
  right: PreviewTrace,
): {
  trace: PreviewTrace;
  spanIdsMap: SpanIdsMap;
} {
  const spans: Record<string, PreviewSpan> = {};
  const spanIdsMap = {};
  const root = diffSpans(spans, spanIdsMap, left?.root, right.root);
  return {
    trace: {
      root,
      spans,
    },
    spanIdsMap,
  };
}

export function getSpanBySpanId<T>({
  spanId,
  spanIdsMap,
  spans,
}: {
  spanId?: string | null;
  spanIdsMap: SpanIdsMap;
  spans: Record<string, T> | null | undefined;
}) {
  if (!spanId) {
    return;
  }
  if (spans?.[spanId]) {
    return spans[spanId];
  }

  const mappedId = spanIdsMap[spanId]?.spanId;
  if (spans?.[mappedId]) {
    return spans[mappedId];
  }

  return;
}

function spanByRightId(
  spans: Record<string, PreviewSpan>,
  left?: PreviewSpan,
  right?: PreviewSpan,
): PreviewSpan {
  if (right && spans[right.span_id]) {
    return spans[right.span_id];
  } else if (left && spans[left.span_id]) {
    return spans[left.span_id];
  }

  if (!left && !right) {
    throw new Error("no spans");
  }

  if (!left) {
    left = makePreviewSpan(initializeEmptySpan(right!));
  }
  if (!right) {
    right = makePreviewSpan(initializeEmptySpan(left!));
  }

  const ret: PreviewSpan = {
    ...left,
    children: [],
  };

  const leftNames = constructSpanChildrenMap(left.children);
  const rightNames = constructSpanChildrenMap(right.children);

  const allNames = new Set([
    ...Object.keys(leftNames),
    ...Object.keys(rightNames),
  ]);

  for (const name of allNames) {
    ret.children.push(spanByRightId(spans, leftNames[name], rightNames[name]));
  }
  sortSpanChildren(ret);
  spans[right.span_id] = ret;

  return ret;
}

/**
 * Constructs a trace whose data is the left trace's data but whose `spans` map is keyed by the right span ids.
 * This enables navigating comparison traces in parallel to the base trace.
 */
export function traceByRightIds(
  left: PreviewTrace | null,
  right: PreviewTrace,
): PreviewTrace {
  const spans: Record<string, PreviewSpan> = {};
  const root = spanByRightId(spans, left?.root, right.root);
  return {
    root,
    spans,
  };
}

export function topSortSpans<T extends Span | PreviewSpan>(
  spans: Record<string, T>,
): T[] {
  const ret: T[] = [];
  const visited: Record<string, boolean> = {};
  const visiting: Record<string, boolean> = {};

  function visit(spanId: string): void {
    if (visited[spanId]) {
      return;
    }

    if (visiting[spanId]) {
      console.warn(`Cycle detected in span graph at span ${spanId}`);
      // Cycle detected, skip
      return;
    }

    visiting[spanId] = true;

    const span = spans[spanId];
    // Visit all parents first
    if (span.span_parents && span.span_parents.length > 0) {
      for (const parentId of span.span_parents) {
        if (parentId in spans) {
          visit(parentId);
        }
      }
    }

    visiting[spanId] = false;
    visited[spanId] = true;
    ret.push(span);
  }

  // Start with all spans
  for (const spanId in spans) {
    visit(spanId);
  }

  return ret;
}

type PreviewSpanData = Pick<
  SpanData,
  | "id"
  | "_xact_id"
  | "span_id"
  | "span_attributes"
  | "metrics"
  | "scores"
  | "tags"
  | "error"
  | "created"
  | "origin"
  | "model"
>;

export interface PreviewSpan {
  id: Span["id"];
  span_id: Span["span_id"];
  root_span_id: Span["root_span_id"];
  parent_span_id: Span["parent_span_id"];
  span_parents: Span["span_parents"];
  scores: Span["scores"];
  data: PreviewSpanData;
  children: PreviewSpan[];
}

export function makePreviewSpan(span: Span): PreviewSpan {
  return {
    id: span.id,
    span_id: span.span_id,
    root_span_id: span.root_span_id,
    parent_span_id: span.parent_span_id,
    span_parents: span.span_parents,
    scores: span.scores,
    data: {
      id: span.data.id,
      _xact_id: span.data._xact_id,
      span_id: span.data.span_id,
      span_attributes: span.data.span_attributes,
      metrics: span.data.metrics,
      scores: span.data.scores,
      tags: span.data.tags,
      error: span.data.error,
      created: span.data.created,
      model: span.data.model,
    },
    children: [],
  };
}
