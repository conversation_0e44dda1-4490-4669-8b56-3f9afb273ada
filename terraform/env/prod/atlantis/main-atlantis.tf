module "atlantis_service" {
  source = "../../../modules/ecs-service"

  service_name     = "atlantis"
  environment      = "production"
  ecs_cluster_name = data.terraform_remote_state.core.outputs.cluster_name
  vpc_config       = data.terraform_remote_state.core.outputs.vpc_config

  container_image      = "ghcr.io/runatlantis/atlantis:${var.atlantis_version}"
  container_port       = 4141
  healthcheck_command  = "curl -f http://localhost:4141/ || exit 1"
  force_new_deployment = var.atlantis_force_new_deployment

  cpu             = 4096
  memory          = 8192
  enable_efs      = true
  efs_mount_point = "/home/<USER>"
  efs_mount_uid   = 100 # looked this up in the container
  efs_mount_gid   = 1000
  task_role_arn   = data.aws_iam_role.atlantis_task_role.arn

  is_public_service = false
  authorized_security_groups = {
    oauth2_proxy = module.oauth2_proxy_service.service_security_group_id
  }
  cpu_scaling    = false
  memory_scaling = false
  # Atlantis is not stateless because it is garbage. Only run a single instance.
  desired_count = 1
  min_count     = 1
  max_count     = 2
  # Shut down the old one before starting the new one. Yes, this is a downtime. No choice with atlantis
  deployment_maximum_percent         = 100
  deployment_minimum_healthy_percent = 0

  environment_variables = {
    ATLANTIS_GH_APP_ID       = "1649747"
    ATLANTIS_GH_APP_SLUG     = "atlantis-braintrust"
    ATLANTIS_WRITE_GIT_CREDS = "true"
    # Be careful enabling other repos that have outside contributors.
    # They will be able to run plans which isn't safe.
    ATLANTIS_REPO_ALLOWLIST  = "github.com/braintrustdata/braintrust"
    ATLANTIS_ATLANTIS_URL    = module.oauth2_proxy_service.service_url
    ATLANTIS_ALLOW_DRAFT_PRS = "true"
    ATLANTIS_ALLOW_FORK_PRS  = "false" # NEVER ENABLE THIS. MAJOR SECURITY RISK.
    # ATLANTIS_AUTOPLAN_MODULES               = "true"
    ATLANTIS_ENABLE_DIFF_MARKDOWN_FORMAT = "false"
    ATLANTIS_QUIET_POLICY_CHECKS         = "true"
    ATLANTIS_HIDE_PREV_PLAN_COMMENTS     = "true"
    ATLANTIS_SILENCE_ALLOWLIST_ERRORS    = "true"
    ATLANTIS_SILENCE_FORK_PR_ERRORS      = "true"
    # This makes atlantis not post comments or responses to PRs that don't have a project in atlantis.yaml
    ATLANTIS_SILENCE_NO_PROJECTS = "true"
    # This makes atlantis not add a Github status to PRs that don't actually generate a plan
    ATLANTIS_SILENCE_VCS_STATUS_NO_PLANS = "false"
    # This makes atlantis not add a Github status to PRs that don't have a project in atlantis.yaml
    ATLANTIS_SILENCE_VCS_STATUS_NO_PROJECTS = "true"
    ATLANTIS_SKIP_CLONE_NO_CHANGES          = "true"
    ATLANTIS_EMOJI_REACTION                 = "eyes"
    # Redis locking configuration
    ATLANTIS_LOCKING_DB_TYPE   = "redis"
    ATLANTIS_REDIS_HOST        = aws_elasticache_serverless_cache.atlantis_redis.endpoint[0].address
    ATLANTIS_REDIS_PORT        = aws_elasticache_serverless_cache.atlantis_redis.endpoint[0].port
    ATLANTIS_REDIS_DB          = "0"
    ATLANTIS_REDIS_TLS_ENABLED = "true"
    # Server-side repo configuration
    # https://www.runatlantis.io/docs/server-configuration.html#repo-config-json
    ATLANTIS_REPO_CONFIG_JSON = jsonencode({
      repos = [
        {
          id = "/.*/"
          autodiscover = {
            mode = "disabled"
          }
        },
        {
          id               = "github.com/braintrustdata/braintrust"
          repo_config_file = "terraform/atlantis.yaml"
          autodiscover = {
            mode = "disabled"
          }
        }
      ]
    })
  }

  secrets = [
    {
      name      = "ATLANTIS_GH_APP_KEY"
      valueFrom = data.aws_secretsmanager_secret.atlantis_github_app_key.arn
    },
    {
      name      = "ATLANTIS_GH_WEBHOOK_SECRET"
      valueFrom = data.aws_secretsmanager_secret.atlantis_github_webhook_secret.arn
    },
    {
      name      = "ATLANTIS_REDIS_PASSWORD"
      valueFrom = data.aws_secretsmanager_secret.atlantis_redis_password.arn
    }
  ]
}

data "aws_iam_role" "atlantis_task_role" {
  # Created in bootstrap/main.tf
  name = "AtlantisTaskRole"
}
